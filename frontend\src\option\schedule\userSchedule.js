export default {
  height:'auto',
  calcHeight: 30,
  tip: false,
  searchShow: true,
  searchMenuSpan: 6,
  border: true,
  index: true,
  viewBtn: true,
  selection: true,
  dialogClickModal: false,
  column: [
    {
      label: "主键，自增",
      prop: "id",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "用户",
      prop: "userId",
      type: "select",
      dicUrl: '/blade-system/user/user-list',
      props: {
        label: 'realName',
        value: 'id'
      },
      addDisplay: true,
      editDisplay: true,
      viewDisplay: false,
      hide: true,
      rules: [
        {
          required: true,
          message: '请选择用户',
          trigger: 'change',
        },
      ],
    },
    {
      label: "用户姓名",
      prop: "userRealName",
      type: "input",
      search: true,
      addDisplay: false,
      editDisplay: false,
      viewDisplay: true,
    },
    {
      label: "工号",
      prop: "userEmployeeNumber",
      type: "input",
      addDisplay: true,
      editDisplay: true,
      viewDisplay: true,
    },
    {
      label: "手机号",
      prop: "userPhone",
      type: "input",
      addDisplay: true,
      editDisplay: true,
      viewDisplay: true,
    },

    {
      label: "酒店",
      prop: "hotelId",
      type: "select",
      dicUrl: '/hy/hotel/list',
      props: {
        label: 'hotelName',
        value: 'id'
      },
      addDisplay: true,
      editDisplay: true,
      viewDisplay: false,
      hide: true,
      search: true,
      rules: [
        {
          required: false,
          message: '请选择酒店',
          trigger: 'change',
        },
      ],
    },
    {
      label: "酒店名称",
      prop: "hotelName",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: true,
    },
    {
      label: "房号",
      prop: "roomNumber",
      type: "input",
      addDisplay: true,
      editDisplay: true,
      viewDisplay: true,
    },
    {
      label: "会议座位号",
      prop: "meetingSeatNumber",
      type: "input",
      addDisplay: true,
      editDisplay: true,
      viewDisplay: true,
    },

    // 已移除日程信息、用餐信息、住宿信息字段配置

    {
      label: "创建人",
      prop: "createUser",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "创建部门",
      prop: "createDept",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "创建时间",
      prop: "createTime",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "更新人",
      prop: "updateUser",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "更新时间",
      prop: "updateTime",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "状态",
      prop: "status",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "是否删除",
      prop: "isDeleted",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
  ]
}
