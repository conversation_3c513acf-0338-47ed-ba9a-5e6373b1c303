export default {
  height: 'auto',
  calcHeight: 30,
  tip: false,
  searchShow: false,
  border: true,
  index: true,
  viewBtn: true,
  selection: false,
  dialogClickModal: false,
  column: [
    {
      label: "主键",
      prop: "id",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "用户ID",
      prop: "userId",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "议程",
      prop: "agendaId",
      type: "select",
      dicUrl: '/hy/agenda/list',
      props: {
        label: 'topic',
        value: 'id'
      },
      addDisplay: true,
      editDisplay: true,
      viewDisplay: true,
      search: true,
      change: ({ value, column, row }) => {
        // 当选择议程后，自动填充相关字段
        // 这个方法会在Vue组件中的handleAgendaChange方法中处理
        return {
          agendaId: value
        };
      },
      rules: [
        {
          required: true,
          message: '请选择议程',
          trigger: 'change',
        },
      ],
    },
    {
      label: "会议日期",
      prop: "agendaDate",
      type: "date",
      format: "YYYY-MM-DD",
      valueFormat: "YYYY-MM-DD HH:mm:ss",
      addDisplay: true,
      editDisplay: true,
      viewDisplay: true,
      rules: [
        {
          required: true,
          message: '请选择会议日期',
          trigger: 'change',
        },
      ],
    },
    {
      label: "开始时间",
      prop: "startTime",
      type: "datetime",
      format: "YYYY-MM-DD HH:mm:ss",
      valueFormat: "YYYY-MM-DD HH:mm:ss",
      addDisplay: true,
      editDisplay: true,
      viewDisplay: true,
      rules: [
        {
          required: true,
          message: '请选择开始时间',
          trigger: 'change',
        },
      ],
    },
    {
      label: "结束时间",
      prop: "endTime",
      type: "datetime",
      format: "YYYY-MM-DD HH:mm:ss",
      valueFormat: "YYYY-MM-DD HH:mm:ss",
      addDisplay: true,
      editDisplay: true,
      viewDisplay: true,
      rules: [
        {
          required: true,
          message: '请选择结束时间',
          trigger: 'change',
        },
      ],
    },
    {
      label: "会议主题",
      prop: "topic",
      type: "textarea",
      addDisplay: true,
      editDisplay: true,
      viewDisplay: true,
      span: 24,
    },
    {
      label: "备注信息",
      prop: "comment",
      type: "textarea",
      addDisplay: true,
      editDisplay: true,
      viewDisplay: true,
      span: 24,
    },
    {
      label: "创建时间",
      prop: "createTime",
      type: "datetime",
      format: "YYYY-MM-DD HH:mm:ss",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: true,
    },
  ]
}
